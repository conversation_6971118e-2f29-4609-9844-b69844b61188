/**
 * File Name : SW_CMRAccountEmailNotification_Test
 * @description : This class is to test batch class scheduled daily to copy data from CMR to Account
 * Copyright : <PERSON>
 * <AUTHOR> <PERSON><PERSON><PERSON> | <EMAIL>
 * 
 * Modification Log 
 * =============================================================== 
 *    Ver  |Date          |Author                   |Modification
 *    1.0  |28-Aug-2023   |@<EMAIL>    |New Class created 
 *    1.1  |22-Jun-2025   |@<EMAIL>    |Enhanced: Cover 5 field copy for InterfaceCheck = 2/3
 */

@IsTest
public class SW_CMRAccountEmailNotification_Test 
{
    @IsTest static void cmrAccountEmailNotiSched() 
    {
        List<User> u = SW_TestDataFactory.createJPNUser(3);
        system.runas(u[0])
        {
            // GIVEN: Setup for InterfaceCheck = 2 (Scenario 5)
            JJ_JPN_CustomerMasterRequest__c cmr2 = new JJ_JPN_CustomerMasterRequest__c(
                Name = 'Test',
                JJ_JPN_PayerCode__c = '1234',
                JJ_JPN_AccountType__c = 'Field Sales(FS)',
                JJ_JPN_PayerNameKanji__c = 'Ｐａｙｅｒ　Ｃｏｄｅ　Ｔｅｓｔ　１',
                JJ_JPN_Information2WDefine__c = '1',
                JJ_JPN_ApprovalStatus__c = 'Approved',
                JJ_JPN_StatusCode__c = 'A',
                Account_Activation_Start_Date__c = getActivationDate(),
                JJ_JPN_BillToCode__c = '12345',
                JJ_JPN_SoldToCode__c = '22222', // unique value
                JJ_JPN_InterfaceCheck__c = 2,
                JJ_JPN_SubCustomerGroup__c = 'SCG000010',
                JJ_JPN_Account_CODE__c = 'ACODE2',
                JJ_JPN_New_Group__c = '1', // valid picklist value
                JJ_JPN_SecondPerson_OfficeName__c = 'Office2',
                JJ_JPN_SecondPersonInCharge__c = u[1].Id,
                JJ_JPN_OldCustomerCode__c = 'OLD2'
            );
            System.debug('7775 Account Activation Date ' + getActivationDate());
            insert cmr2;

            Account acc2 = SW_TestDataFactory.createCMRaccount();
            acc2.OutletNumber__c = cmr2.JJ_JPN_SoldToCode__c;
            acc2.AccountNumber = 'ACC200'; // unique value
            update acc2;

            // GIVEN: Setup for InterfaceCheck = 3 (Scenario 3)
            JJ_JPN_CustomerMasterRequest__c cmr3 = new JJ_JPN_CustomerMasterRequest__c(
                Name = 'Test',
                JJ_JPN_PayerCode__c = '1234',
                JJ_JPN_AccountType__c = 'Field Sales(FS)',
                JJ_JPN_PayerNameKanji__c = 'Ｐａｙｅｒ　Ｃｏｄｅ　Ｔｅｓｔ　１',
                JJ_JPN_Information2WDefine__c = '1',
                JJ_JPN_ApprovalStatus__c = 'Approved',
                JJ_JPN_StatusCode__c = 'A',
                Account_Activation_Start_Date__c = getActivationDate(),
                JJ_JPN_BillToCode__c = '12345',
                JJ_JPN_SoldToCode__c = '33333', // unique value
                JJ_JPN_InterfaceCheck__c = 3,
                JJ_JPN_SubCustomerGroup__c = 'SCG000010',
                JJ_JPN_Account_CODE__c = 'ACODE3',
                JJ_JPN_New_Group__c = '2', // valid picklist value
                JJ_JPN_SecondPerson_OfficeName__c = 'Office3',
                JJ_JPN_SecondPersonInCharge__c = u[2].Id,
                JJ_JPN_OldCustomerCode__c = 'OLD3'
            );
            insert cmr3;

            Account acc3 = SW_TestDataFactory.createCMRaccount();
            acc3.OutletNumber__c = cmr3.JJ_JPN_SoldToCode__c;
            acc3.AccountNumber = 'ACC300'; // unique value
            update acc3;

            // GIVEN: Setup for default scenario (other fields/scenarios)
            JJ_JPN_CustomerMasterRequest__c cmr = SW_TestDataFactory.createSinglecmr();
            cmr.JJ_JPN_SoldToCode__c = '44444'; // unique value
            update cmr;
            Account acc = SW_TestDataFactory.createCMRaccount();
            acc.OutletNumber__c = cmr.JJ_JPN_SoldToCode__c;
            acc.AccountNumber = 'ACC400'; // unique value
            update acc;

            // WHEN: Run all batch jobs and scheduled jobs within a single test context
            Test.startTest();
            Database.executeBatch(new SW_CMRAccountEmailNotification()); // covers all, due to how your batch selector works
            SW_CMRAccountEmailNotification testsche = new SW_CMRAccountEmailNotification();
            String sch = '0 0 23 * * ?';
            system.schedule('Test', sch, testsche);
            Test.stopTest();

            // THEN: assertions for InterfaceCheck = 2
            acc2 = [SELECT JJ_JPN_Account_CODE__c, JJ_JPN_New_Group__c, JJ_JPN_SecondPerson_OfficeName__c, JJ_JPN_SecondPersonInCharge__c, JJ_JPN_OldCustomerCode__c FROM Account WHERE Id = :acc2.Id];
            Assert.areEqual('ACODE2', acc2.JJ_JPN_Account_CODE__c, 'Account CODE should match for InterfaceCheck=2');
            Assert.areEqual('1', acc2.JJ_JPN_New_Group__c, 'New Group should match for InterfaceCheck=2');
            Assert.areEqual('Office2', acc2.JJ_JPN_SecondPerson_OfficeName__c, 'Office Name should match for InterfaceCheck=2');
            Assert.areEqual(u[1].Id, acc2.JJ_JPN_SecondPersonInCharge__c, 'Second Person should match for InterfaceCheck=2');
            Assert.areEqual('OLD2', acc2.JJ_JPN_OldCustomerCode__c, 'Old Customer Code should match for InterfaceCheck=2');

            // THEN: assertions for InterfaceCheck = 3
            acc3 = [SELECT JJ_JPN_Account_CODE__c, JJ_JPN_New_Group__c, JJ_JPN_SecondPerson_OfficeName__c, JJ_JPN_SecondPersonInCharge__c, JJ_JPN_OldCustomerCode__c FROM Account WHERE Id = :acc3.Id];
            Assert.areEqual('ACODE3', acc3.JJ_JPN_Account_CODE__c, 'Account CODE should match for InterfaceCheck=3');
            Assert.areEqual('2', acc3.JJ_JPN_New_Group__c, 'New Group should match for InterfaceCheck=3');
            Assert.areEqual('Office3', acc3.JJ_JPN_SecondPerson_OfficeName__c, 'Office Name should match for InterfaceCheck=3');
            Assert.areEqual(u[2].Id, acc3.JJ_JPN_SecondPersonInCharge__c, 'Second Person should match for InterfaceCheck=3');
            Assert.areEqual('OLD3', acc3.JJ_JPN_OldCustomerCode__c, 'Old Customer Code should match for InterfaceCheck=3');

            // THEN: assertions for default scenario
            acc = [SELECT JJ_JPN_Information2WDefine__c FROM Account WHERE Id = :acc.Id];
            Assert.areEqual(cmr.JJ_JPN_Information2WDefine__c, acc.JJ_JPN_Information2WDefine__c, 'Value should match');
        }
    }
    
    private static Date getActivationDate(){
        if(DateTime.now().format('E') == 'Mon'){
            return system.today() + 1;
        }
        return system.today();

    }
}